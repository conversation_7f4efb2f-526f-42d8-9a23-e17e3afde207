import type { MessageRemindItem, MessageRemindParams } from './types'
/**
 * 消息提醒相关 API
 */
import { http } from '@shencom/request'
import { url } from './config'

/**
 * 获取消息提醒列表
 * @param params 请求参数
 * @returns 消息提醒响应数据
 */
export async function fetchMessageRemindList(params: MessageRemindParams) {
  const res = await http.post<SC.API.IndexInterface<MessageRemindItem>>(`${url}/xsgc/message/remind/index`, params)

  return res.data
}

/**
 * 获取我的待阅消息
 * @param size 每页数量，默认5条
 * @param page 页码，默认0
 * @returns 消息提醒响应数据
 */
export async function fetchUnreadMessages(size = 5, page = 0) {
  return fetchMessageRemindList({
    type: [6, 7], // 接入监管和结束监管
    status: 0, // 未读
    size,
    page,
  })
}

/**
 * 获取我的已阅消息
 * @param size 每页数量，默认5条
 * @param page 页码，默认0
 * @returns 消息提醒响应数据
 */
export async function fetchReadMessages(size = 5, page = 0) {
  return fetchMessageRemindList({
    type: [6, 7], // 接入监管和结束监管
    status: 1, // 已读
    size,
    page,
  })
}
