/**
 * shenanPioneer 模块 API 类型定义
 */

/** 项目统计数据 */
export interface ProjectStatistics {
  /** 施工单位数量 */
  contractingUnitCount: number
  /** 工程总数 */
  totalProjectCount: number
  /** 施工中工程数量 */
  ongoingProjectCount: number
  /** 工程总金额（万元） */
  totalAmount: number
}

/** 违规告警趋势数据项 */
export interface ViolationTrendItem {
  /** 事件数量 */
  eventCount: number
  /** 事件日期 */
  eventDate: string
}

/** 违规告警趋势请求参数 */
export interface ViolationTrendParams {
  /** 开始日期 */
  startDate?: string
  /** 结束日期 */
  endDate?: string
}

/** 工程区域分布数据项 */
export interface ProjectRegionItem {
  /** 工程数量 */
  projectCount: number
  /** 区域标题 */
  title: string
  /** 区域类型 */
  type: string
}

/** 工程区域分布请求参数 */
export interface ProjectRegionParams {
  /** 区域ID */
  regionId: string
  /** 区域父级ID */
  regionPid: string
  /** 区域子级ID */
  regionCid: string
}

/** 用户区域关联信息中的区域ID项 */
export interface UserRegionIdItem {
  /** 创建时间 */
  createdAt: number
  /** ID */
  id: string
  /** 是否删除 */
  isDeleted: number
  /** 区域父级ID */
  regionPid: string
  /** 关联ID */
  relateId: string
  /** 更新时间 */
  updatedAt: number
}

/** 用户区域关联信息 */
export interface UserRegionRelateInfo {
  /** 激活状态 */
  active: number
  /** 创建时间 */
  createdAt: number
  /** ID */
  id: string
  /** 级别 */
  level: number
  /** 成员ID */
  memberId: string
  /** 区域ID列表 */
  regionIds: UserRegionIdItem[]
  /** 类型ID */
  typeId: string
  /** 更新时间 */
  updatedAt: number
}

/** 消息提醒项 */
export interface MessageRemindItem {
  /** 消息内容 */
  content: string
  /** 创建时间 */
  createdAt: number
  /** 消息ID */
  id: string
  /** 订单ID */
  orderId: string
  /** 项目ID */
  projectId: string
  /** 关联ID */
  relateId: string
  /** 解决标志 */
  resolveFlag: number
  /** 状态：0-未读，1-已读 */
  status: number
  /** 类型：6-接入监管，7-结束监管 */
  type: number
  /** 更新时间 */
  updatedAt: number
  /** 用户ID */
  userId: string
}

/** 消息提醒请求参数 */
export interface MessageRemindParams {
  /** 消息类型，支持多个值 */
  type: number[]
  /** 状态：0-未读，1-已读 */
  status: number
  /** 每页数量 */
  size: number
  /** 页码，从0开始 */
  page?: number
}

/** 分页信息 */
export interface PageableInfo {
  /** 偏移量 */
  offset: number
  /** 页码 */
  pageNumber: number
  /** 每页大小 */
  pageSize: number
  /** 是否分页 */
  paged: boolean
  /** 排序信息 */
  sort: {
    empty: boolean
    sorted: boolean
    unsorted: boolean
  }
  /** 是否未分页 */
  unpaged: boolean
}

/** 消息提醒响应数据 */
export interface MessageRemindResponse {
  /** 消息列表 */
  content: MessageRemindItem[]
  /** 是否为空 */
  empty: boolean
  /** 是否为第一页 */
  first: boolean
  /** 是否为最后一页 */
  last: boolean
  /** 当前页码 */
  number: number
  /** 当前页元素数量 */
  numberOfElements: number
  /** 分页信息 */
  pageable: PageableInfo
  /** 每页大小 */
  size: number
  /** 排序信息 */
  sort: {
    empty: boolean
    sorted: boolean
    unsorted: boolean
  }
  /** 总元素数量 */
  totalElements: number
  /** 总页数 */
  totalPages: number
}
