import type { RegionTree } from '@shenanPioneer/api'
import { fetchRegionTree, fetchUserRegionTree } from '@shenanPioneer/api'
import { TreeFindNode, TreeToArray } from '@shencom/utils'
import { formatterOptions } from '../utils'

// 通过deep获取对应深度的数据,只保留指定层级的数据
function processRegionTree(data: RegionTree[], targetLevel: number, currentLevel = 1): RegionTree[] {
  if (!data || data.length === 0) {
    return []
  }

  return data.map((item) => {
    const newItem = { ...item }
    if (currentLevel < targetLevel && newItem.children && newItem.children.length > 0) {
      newItem.children = processRegionTree(newItem.children, targetLevel, currentLevel + 1)
    }
    else {
      // 如果已经达到目标层级，移除所有子节点
      newItem.children = undefined
    }

    return newItem
  })
}

function CacheData(key: string): RegionTree[]
function CacheData(key: string, data: RegionTree[] | null): void
function CacheData(key: string, data?: RegionTree[] | null) {
  if (data === undefined) {
    return JSON.parse(localStorage.getItem(key) || 'null') as RegionTree[]
  }

  if (data === null) {
    localStorage.removeItem(key)
  }
  else if (data?.length) {
    localStorage.setItem(key, JSON.stringify(data))
  }
}

// 定义纯Pinia store
const regionAuthStore = defineStore('shenanPioneer/regionAuth', () => {
  const key = 'shenanPioneer_regionAuth'
  // 加载状态
  const loading = ref(false)
  // 错误信息
  const error = ref<string | null>(null)
  // 是否已加载数据
  const loaded = ref(false)

  // 所有区域树数据
  const allRegionTrees = ref<RegionTree[]>(CacheData(key) || [])

  // 异步加载指定父ID的区域树
  const fetchRegionTrees = async () => {
    if (loading.value) {
      return
    }

    loading.value = true
    error.value = null

    try {
      const data = await fetchUserRegionTree({
        pId: '2', // 默认深圳市
        deep: 3, // 默认深度3级
        root: true, // 不返回顶节点
      })

      if (data) {
        allRegionTrees.value = data || []
        CacheData(key, data)
      }

      loaded.value = true
    }
    catch (err) {
      error.value = err instanceof Error ? err.message : '获取区域树失败'
      allRegionTrees.value = []
    }
    finally {
      loading.value = false
    }
  }

  // 查询区域树，如果未加载则先加载
  const queryRegionTree = async () => {
    if (!allRegionTrees.value.length) {
      await fetchRegionTrees()
    }
  }

  // 清除区域树
  const clearRegionTree = () => {
    allRegionTrees.value = []
    loaded.value = false
    error.value = null
    CacheData(key, null)
  }

  // 刷新区域树
  const refreshRegionTree = async () => {
    clearRegionTree()
    await fetchRegionTrees()
  }

  return {
    data: allRegionTrees,
    queryRegionTree,
    clearRegionTree,
    refreshRegionTree,
  }
})

// 定义纯Pinia store
const regionStore = defineStore('shenanPioneer/region', () => {
  const key = 'shenanPioneer_region'
  // 加载状态
  const loading = ref(false)
  // 错误信息
  const error = ref<string | null>(null)
  // 是否已加载数据
  const loaded = ref(false)

  // 所有区域树数据
  const allRegionTrees = ref<RegionTree[]>(CacheData(key) || [])

  // 异步加载指定父ID的区域树
  const fetchRegionTrees = async () => {
    if (loading.value) {
      return
    }

    loading.value = true
    error.value = null

    try {
      const data = await fetchRegionTree({
        pId: '2', // 默认深圳市
        deep: 3, // 默认深度3级
        root: true, // 不返回顶节点
      })

      if (data) {
        allRegionTrees.value = data || []
        CacheData(key, data)
      }

      loaded.value = true
    }
    catch (err) {
      error.value = err instanceof Error ? err.message : '获取区域树失败'
      allRegionTrees.value = []
    }
    finally {
      loading.value = false
    }
  }

  // 查询区域树，如果未加载则先加载
  const queryRegionTree = async () => {
    if (!allRegionTrees.value.length) {
      await fetchRegionTrees()
    }
  }

  // 清除区域树
  const clearRegionTree = () => {
    allRegionTrees.value = []
    loaded.value = false
    error.value = null
    CacheData(key, null)
  }

  // 刷新区域树
  const refreshRegionTree = async () => {
    clearRegionTree()
    await fetchRegionTrees()
  }

  return {
    data: allRegionTrees,
    queryRegionTree,
    clearRegionTree,
    refreshRegionTree,
  }
})

// 导出store实例方法
export const useBaseRegionStore = () => regionStore()
export const useBaseRegionAuthStore = () => regionAuthStore()

function regionHook(store: ReturnType<typeof useBaseRegionStore | typeof useBaseRegionAuthStore>, deep: number = 3, root: boolean = true) {
  const regionTree = computed(() => {
    let list: RegionTree[] = []
    // 如果顶节点，则只返回顶节点
    if (root) {
      list = store.data
    }

    // 如果非顶节点，则只返回顶节点的子节点
    else {
      list = store.data?.[0]?.children || []
    }

    // 如果深度不为空，则只返回指定深度的数据
    if (deep) {
      // 如果如果需要顶层数据,需要+1
      const deepNum = root ? deep + 1 : deep
      list = processRegionTree(list, deepNum)
    }

    return list
  })

  const regionList = computed(() => TreeToArray(regionTree.value))
  const regionOptions = computed(() => formatterOptions(regionList.value, {
    label: 'title',
    value: 'id',
  }))

  // 获取当前id的区域项
  const getRegionItem = (id: string) => {
    const list = regionList.value
    return list.find(item => item.id === id)
  }

  // ids to names
  const getRegionNames = (ids: string[]): string[] => {
    return ids.map(id => getRegionItem(id)?.title || '')
  }

  // 获取某个id下面的所有子节点
  const getRegionTreeById = (id: string) => TreeFindNode(regionTree.value, id)

  // 查询区域树，如果未加载则先加载
  const query = async () => {
    await store.queryRegionTree()
  }

  // 刷新区域树
  const refesh = async () => {
    await store.refreshRegionTree()
  }

  const clear = () => {
    store.clearRegionTree()
  }

  // 在组件挂载时自动加载数据
  onMounted(() => {
    query()
  })

  // 返回处理后的属性和方法
  return {
    // 直接返回计算属性，避免嵌套
    regionTree,
    regionList,
    regionOptions,
    getRegionTreeById,
    getRegionItem,
    getRegionNames,

    query,
    refesh,
    clear,
  }
}

// 定义hook函数，不再接收pId参数
export function useRegionStore(deep: number = 3, root: boolean = true) {
  const store = regionStore()

  return regionHook(store, deep, root)
}

export function useRegionAuthStore(deep: number = 3, root: boolean = true) {
  const store = regionAuthStore()
  return regionHook(store, deep, root)
}
