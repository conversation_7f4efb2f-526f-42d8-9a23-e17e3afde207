<script setup lang="ts">
import type { CascaderValue } from 'element-plus'
import defu from 'defu'
import { useRegionAuthStore, useRegionStore } from '../../store/region'

interface Props {
  modelValue?: CascaderValue
  root?: boolean
  deep?: 1 | 2 | 3
  size?: 'default' | 'small' | 'large'
  /** 是否是授权区域 */
  isAuthRegion?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: CascaderValue): void
  (e: 'change', value: string | string[]): void
}>()

const emptyValue = ref<string>('暂无数据')

const regionStore = props.isAuthRegion ? useRegionAuthStore(props.deep, props.root) : useRegionStore(props.deep, props.root)

// 默认配置
const defaultProps = {
  clearable: true,
  filterable: true,
  placeholder: '请选择',
  props: {
    value: 'id',
    label: 'title',
    expandTrigger: 'hover',
  },
}

// 区域数据
const regionOptions = computed(() => regionStore.regionTree.value || [])
// 加载状态
const loading = ref(false)

// 当前选中的值
const currentValue = ref<CascaderValue | undefined>(props.modelValue)

watch(() => props.modelValue, (newValue) => {
  currentValue.value = newValue
}, { deep: true })

// 处理级联选择器变化
function handleChange(value: CascaderValue) {
  currentValue.value = value
  emit('update:modelValue', value)
  emit('change', value as string | string[])
}

const attrs = useAttrs()

const elCascaderProps = computed(() => {
  const attr = defu({ ...attrs, size: props.size }, defaultProps) as Props
  return attr
})

function onRefresh() {
  regionStore.refesh()
}
</script>

<template>
  <div class="size-full flex items-center">
    <ElCascader
      v-model="currentValue" :options="regionOptions" v-bind="elCascaderProps" class="flex-1"
      @change="handleChange"
    >
      <template #empty>
        <div class="text-center text-gray-500">
          {{ emptyValue }}
        </div>
      </template>
    </ElCascader>
    <el-button plain class="ml-2" :size="elCascaderProps?.size || 'default'" @click="onRefresh">
      <FaIcon :name="loading ? 'i-line-md:loading-loop' : 'ep:refresh'" />
    </el-button>
  </div>
</template>
