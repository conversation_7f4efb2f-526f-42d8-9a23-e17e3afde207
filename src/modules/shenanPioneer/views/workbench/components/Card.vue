<script setup lang="ts">
interface IProps {
  title?: string
}

defineProps<IProps>()
</script>

<template>
  <div class="flex flex-col rounded-lg bg-background p-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center text-base font-semibold">
        <i class="mr-2 h-4 w-1 rounded-sm bg-blue-500" />
        <span>{{ title }}</span>
      </div>
      <slot name="header" />
    </div>
    <slot />
  </div>
</template>
