<!--
  待阅事项组件
  显示用户的待阅任务列表，支持标签页切换
-->
<script setup lang="ts">
import type { TagProps } from 'element-plus'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import type { MessageRemindItem } from '@shenanPioneer/api'
import { getReadMessages, getUnreadMessages } from '@shenanPioneer/api'
import Card from './Card.vue'

/**
 * 待阅事项接口
 */
interface ReadingItem {
  /** 唯一标识 */
  id: string
  /** 日期 */
  date: string
  /** 标题 */
  title: string
  /** 状态 */
  status: string
  /** 原始数据 */
  raw?: MessageRemindItem
}

const radioOptions = ref([
  { label: '我的待阅', value: 1 },
  { label: '我的已阅', value: 2 },
])

const filterOptions = ref(['今日', '昨日', '全部'])

// ==================== 响应式数据 ====================
const loading = ref(false)
const activeTab = ref<number>(1)
const selectedFilters = ref<string>('今日')
const readingList = ref<ReadingItem[]>([])
const totalCount = ref(0)

// ==================== 计算属性 ====================
const filteredReadingList = computed(() => {
  let filtered = readingList.value

  // 根据时间过滤
  if (selectedFilters.value !== '全部') {
    const now = dayjs()
    const today = now.format('YYYY-MM-DD')
    const yesterday = now.subtract(1, 'day').format('YYYY-MM-DD')

    filtered = filtered.filter((item) => {
      const itemDate = dayjs(item.raw?.createdAt).format('YYYY-MM-DD')
      if (selectedFilters.value === '今日') {
        return itemDate === today
      }
      else if (selectedFilters.value === '昨日') {
        return itemDate === yesterday
      }
      return true
    })
  }

  return filtered.slice(0, 5)
})

// ==================== 方法定义 ====================
/**
 * 转换消息数据为显示格式
 * @param items - 消息列表
 * @returns 转换后的数据
 */
function transformMessageData(items: MessageRemindItem[]): ReadingItem[] {
  return items.map((item) => {
    // 提取工程名称
    const titleMatch = item.content.match(/工程【(.+?)】/)
    const projectName = titleMatch ? titleMatch[1] : '未知工程'

    // 格式化日期
    const date = dayjs(item.createdAt).format('MM-DD')

    // 确定状态文本
    let status = ''
    if (item.type === 6) {
      status = '已接入监管'
    }
    else if (item.type === 7) {
      status = '已结束监管'
    }

    return {
      id: item.id,
      date,
      title: `工程【${projectName}】`,
      status,
      raw: item,
    }
  })
}

/**
 * 获取消息数据
 */
async function fetchMessages(): Promise<void> {
  loading.value = true
  try {
    let response
    if (activeTab.value === 1) {
      // 我的待阅
      response = await getUnreadMessages(5, 0)
    }
    else {
      // 我的已阅
      response = await getReadMessages(5, 0)
    }

    readingList.value = transformMessageData(response.content)
    totalCount.value = response.totalElements
  }
  catch (error) {
    console.error('获取消息数据失败:', error)
    ElMessage.error('获取消息数据失败')
    readingList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 处理阅读项点击
 * @param item - 阅读项
 */
function handleReadingClick(item: ReadingItem): void {
  // 处理阅读项点击事件
  console.log('点击阅读项:', item)
}

/**
 * 处理更多点击
 */
function handleMoreClick(): void {
  // 处理更多点击
  console.log('点击更多')
}

/**
 * 处理标签页变化
 */
function handleTabChange(): void {
  // 重新获取数据
  fetchMessages()
}

/**
 * 获取状态类型
 * @param status - 状态
 * @returns 类型
 */
function getStatusType(status: string): TagProps['type'] {
  switch (status) {
    case '已接入监管':
      return 'success'
    case '待接入监管':
      return 'primary'
    case '已结束监管':
      return 'info'
    default:
      return 'info'
  }
}

// ==================== 生命周期 ====================
onMounted(() => {
  fetchMessages()
})

// ==================== 监听器 ====================
watch(activeTab, () => {
  handleTabChange()
})

watch(selectedFilters, () => {
  // 时间过滤变化时不需要重新请求接口，只需要重新计算过滤结果
})
</script>

<template>
  <Card title="待阅事项">
    <template #header>
      <el-radio-group v-model="selectedFilters" size="small" :disabled="loading">
        <el-radio-button v-for="filter in filterOptions" :key="filter" :label="filter">
          {{ filter }}
        </el-radio-button>
      </el-radio-group>
    </template>

    <div class="py-2">
      <el-radio-group v-model="activeTab" :disabled="loading">
        <el-radio-button
          v-for="item in radioOptions" :key="item.value" v-model="activeTab" :label="item.value"
          size="small"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <div v-loading="loading" class="flex-1">
      <div class="h-full flex flex-col">
        <div class="flex-1">
          <div v-if="filteredReadingList.length === 0 && !loading" class="flex items-center justify-center py-8 text-gray-500">
            暂无数据
          </div>
          <div
            v-for="item in filteredReadingList" :key="item.id"
            class="flex cursor-pointer items-center py-3 hover:text-primary" @click="handleReadingClick(item)"
          >
            <span class="mr-2 text-sm text-foreground/60">{{ item.date }}</span>
            <span class="flex-1 text-sm leading-relaxed ellipsis-1">{{ item.title }}</span>
            <ElTag v-if="item.status" :type="getStatusType(item.status)" size="small" class="ml-2">
              {{ item.status }}
            </ElTag>
          </div>
        </div>

        <div
          v-if="totalCount > 5"
          class="flex cursor-pointer items-center justify-center rounded p-2 text-sm text-blue-500 transition-colors duration-300 hover:bg-blue-50"
          @click="handleMoreClick"
        >
          <span class="mr-1">更多</span>
          <FaIcon name="i-ep:arrow-right" />
        </div>
      </div>
    </div>
  </Card>
</template>
