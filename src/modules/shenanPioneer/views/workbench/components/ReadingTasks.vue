<!--
  待阅事项组件
  显示用户的待阅任务列表，支持标签页切换
-->
<script setup lang="ts">
import type { TagProps } from 'element-plus'
import Card from './Card.vue'

/**
 * 待阅事项接口
 */
interface ReadingItem {
  /** 唯一标识 */
  id?: number
  /** 日期 */
  date: string
  /** 标题 */
  title: string
  /** 状态 */
  status: string
}

const data = ref([...Array.from({ length: 10 }).map((_, index) => ({
  id: index + 1,
  date: `05-0${index + 1}`,
  title: `工程【小散工程名称${index}${1}】`,
  status: index % 4 === 0 ? '已接入监管' : index % 4 === 1 ? '待接入监管' : '已结束监管',
}))])

const radioOptions = ref([
  { label: '我的待阅', value: 1 },
  { label: '我的已阅', value: 2 },
])

const filterOptions = ref(['今日', '昨日', '全部'])

// ==================== 响应式数据 ====================
const activeTab = ref<number>(1)
const selectedFilters = ref<string>('今日')
const readingList = ref<ReadingItem[]>(data.value)

// ==================== 计算属性 ====================
const filteredReadingList = computed(() => {
  // 这里可以根据 activeTab 和 selectedFilters 进行过滤，限制15条
  return readingList.value.slice(0, 5)
})

// ==================== 方法定义 ====================
/**
 * 处理阅读项点击
 * @param item - 阅读项
 */
function handleReadingClick(item: ReadingItem): void {
  // 处理阅读项点击事件
}

/**
 * 处理更多点击
 */
function handleMoreClick(): void {
// 处理更多点击
}

/**
 * 处理标签页变化
 * @param tab - 标签页
 */
function handleTabChange(tab: string): void {
  // 处理标签页变化
}

/**
 * 获取状态类型
 * @param status - 状态
 * @returns 类型
 */
function getStatusType(status: string): TagProps['type'] {
  switch (status) {
    case '已接入监管':
      return 'success'
    case '待接入监管':
      return 'primary'
    case '已结束监管':
      return 'info'
    default:
      return 'info'
  }
}

// ==================== 监听器 ====================
watch(activeTab, (newValue) => {
  handleTabChange(String(newValue))
})
</script>

<template>
  <Card title="待阅事项">
    <template #header>
      <el-radio-group v-model="selectedFilters" size="small">
        <el-radio-button v-for="filter in filterOptions" :key="filter" :label="filter">
          {{ filter }}
        </el-radio-button>
      </el-radio-group>
    </template>

    <div class="py-2">
      <el-radio-group v-model="activeTab">
        <el-radio-button
          v-for="item in radioOptions" :key="item.value" v-model="activeTab" :label="item.value"
          size="small"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <div class="flex-1">
      <div class="h-full flex flex-col">
        <div class="flex-1">
          <div
            v-for="item in filteredReadingList" :key="item.id"
            class="flex cursor-pointer items-center py-3 hover:text-primary" @click="handleReadingClick(item)"
          >
            <span class="mr-2 text-sm text-foreground/60">{{ item.date }}</span>
            <span class="flex-1 text-sm leading-relaxed ellipsis-1">{{ item.title }}</span>
            <ElTag v-if="item.status" :type="getStatusType(item.status)" size="small" class="ml-2">
              {{ item.status }}
            </ElTag>
          </div>
        </div>

        <div
          class="flex cursor-pointer items-center justify-center rounded p-2 text-sm text-blue-500 transition-colors duration-300 hover:bg-blue-50"
          @click="handleMoreClick"
        >
          <span class="mr-1">更多</span>
          <FaIcon name="i-ep:arrow-right" />
        </div>
      </div>
    </div>
  </Card>
</template>
