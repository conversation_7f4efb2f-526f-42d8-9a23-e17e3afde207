<!--
  违规告警趋势图组件
  使用 AntV G2 显示告警趋势折线图
-->
<script setup lang="ts">
import type { ViolationTrendItem } from '@shenanPioneer/api'

import { Chart } from '@antv/g2'
import { fetchViolationTrend } from '@shenanPioneer/api'
import { Dayjs, FormatDate } from '@shencom/utils'

import Card from './Card.vue'

/**
 * 图表数据项接口
 */
interface ChartDataItem {
  /** 日期 */
  date: string
  /** 告警次数 */
  count: number
}
// ==================== 响应式数据 ====================
const settingsStore = useSettingsStore()
const chartContainer = ref<HTMLElement>()
const dateRange = ref<string[]>([FormatDate(Dayjs().subtract(7, 'day')), FormatDate()])
const data = ref<ChartDataItem[]>([])
const loading = ref(false)
let chart: Chart | null = null

// ==================== 主题相关 ====================
/**
 * 是否为暗色主题
 */
const isDarkTheme = computed(() => settingsStore.currentColorScheme === 'dark')

/**
 * 转换 API 数据为图表数据格式
 * @param apiData API 返回的数据
 * @returns 图表数据
 */
function transformApiDataToChartData(apiData: ViolationTrendItem[]): ChartDataItem[] {
  return apiData.map(item => ({
    date: item.eventDate,
    count: item.eventCount,
  }))
}

/**
 * 获取违规告警趋势数据
 */
async function fetchViolationTrendData(): Promise<void> {
  try {
    loading.value = true
    const [startDate, endDate] = dateRange.value
    const apiData = await fetchViolationTrend({
      startDate,
      endDate,
    })
    data.value = transformApiDataToChartData(apiData.data)
    updateChart()
  }
  catch (error) {
    console.error('获取违规告警趋势数据失败:', error)
    // 发生错误时使用空数据
    data.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 初始化图表
 */
function initChart() {
  if (!chartContainer.value) {
    return
  }

  chart = new Chart({
    container: chartContainer.value,
    autoFit: true,
    height: 350,
    theme: isDarkTheme.value ? 'dark' : 'light',
  })

  chart
    .line()
    .data(data.value)
    .encode('x', 'date')
    .encode('y', 'count')
    .title('告警趋势')
    .axis('x', { title: '日期' })
    .axis('y', { title: '警告次数' })
    .tooltip({
      title: 'date',
      items: [
        {
          field: 'count',
          name: '告警次数',
          valueFormatter: (value: number) => `${value} 次`,
        },
      ],
    })

  // 添加面积填充
  chart
    .area()
    .data(data.value)
    .encode('x', 'date')
    .encode('y', 'count')
    .style('fillOpacity', 0.3)
    .tooltip(false)

  chart.render()
}

/**
 * 更新图表主题
 */
function updateChartTheme() {
  if (!chart) {
    return
  }

  chart.theme({ type: isDarkTheme.value ? 'dark' : 'light' })
  chart.render()
}

/**
 * 销毁图表
 */
function destroyChart() {
  if (chart) {
    chart.destroy()
    chart = null
  }
}

/**
 * 处理日期范围变化
 * @param value - 日期范围
 */
function handleDateChange(value: string[]) {
  dateRange.value = value
  // 日期变化时重新获取数据
  fetchViolationTrendData()
}

/**
 * 更新图表数据
 */
function updateChart() {
  if (!chart) {
    return
  }
  chart.changeData(data.value)
}

/**
 * 重新渲染图表
 */
function resizeChart() {
  if (chart) {
    chart.forceFit()
  }
}

// 监听窗口大小变化
let resizeObserver: ResizeObserver | null = null

// ==================== 生命周期 ====================
onMounted(async () => {
  await fetchViolationTrendData()
  initChart()

  // 设置窗口大小监听
  if (chartContainer.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      resizeChart()
    })
    resizeObserver.observe(chartContainer.value)
  }
})

onUnmounted(() => {
  destroyChart()
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// ==================== 监听器 ====================

// 监听主题变化
watch(
  isDarkTheme,
  () => {
    updateChartTheme()
  },
  { deep: true },
)
</script>

<template>
  <Card title="违规告警趋势图">
    <template #header>
      <div class="w-full flex items-center justify-end gap-3 md:w-auto md:justify-start">
        <el-date-picker
          v-model="dateRange" type="daterange" range-separator="—" start-placeholder="开始日期"
          end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="loading"
          @change="handleDateChange"
        />
      </div>
    </template>

    <div class="mt-2">
      <div v-loading="loading" class="min-h-80 w-full">
        <div ref="chartContainer" class="min-h-80 w-full" />
      </div>
    </div>
  </Card>
</template>
