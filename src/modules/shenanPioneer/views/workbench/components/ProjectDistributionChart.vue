<!--
  工程区域分布组件
  使用 AntV G2 显示工程区域分布柱状图
-->
<script setup lang="ts">
import type { ProjectRegionItem, ProjectRegionParams, RegionTree } from '@shenanPioneer/api'
import { Chart } from '@antv/g2'
import { fetchProjectRegionStatistics, fetchUserRegionTree } from '@shenanPioneer/api'
import RegionCascader from '@shenanPioneer/components/RegionCascader/RegionCascader.vue'
import Card from './Card.vue'

// ==================== 类型定义 ====================
interface ChartDataItem {
  /** 区域名称 */
  region: string
  /** 工程数量 */
  count: number
}

// ==================== Store ====================
const settingsStore = useSettingsStore()

// ==================== 计算属性 ====================
/**
 * 是否为暗色主题
 */
const isDarkTheme = computed(() => settingsStore.currentColorScheme === 'dark')

/**
 * 当前主题名称
 */
const currentThemeName = computed(() => {
  if (typeof document !== 'undefined') {
    return document.body.getAttribute('data-theme') || 'light'
  }
  return 'light'
})

// ==================== 响应式数据 ====================
const chartContainer = ref<HTMLElement>()
const loading = ref<boolean>(false)
let chart: Chart | null = null
const data = ref<ChartDataItem[]>([])
const regionOptions = ref<RegionTree[]>([])
const region = ref<string[]>([])

// API 请求参数
const requestParams = reactive<ProjectRegionParams>({
  regionId: '',
  regionPid: '',
  regionCid: '',
})
// ==================== 方法定义 ====================
/**
 * 获取工程区域分布数据
 */
async function fetchProjectRegionData() {
  try {
    loading.value = true
    const response = await fetchProjectRegionStatistics(requestParams)

    // 将 API 数据转换为图表所需格式
    data.value = response.data.map((item: ProjectRegionItem) => ({
      region: item.title,
      count: item.projectCount,
    }))

    // 更新图表数据
    updateChart()
  }
  catch (error) {
    console.error('获取工程区域分布数据失败:', error)
    // 发生错误时使用空数据
    data.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 获取用户区域树
 */
async function getUserRegionTree() {
  try {
    const response = await fetchUserRegionTree()
    regionOptions.value = response

    if (regionOptions.value.length) {
      region.value = [regionOptions.value[0].id]
    }
  }
  catch (error) {
    console.error('获取用户区域树失败:', error)
  }
}

/**
 * 初始化图表
 */
function initChart() {
  if (!chartContainer.value) {
    return
  }

  chart = new Chart({
    container: chartContainer.value,
    autoFit: true,
    height: 350,
    theme: isDarkTheme.value ? 'dark' : 'light',
  })

  chart
    .interval()
    .data(data.value)
    .encode('x', 'region')
    .encode('y', 'count')
    .axis('x', { title: '区域' })
    .axis('y', { title: '工程数量' })
    .encode('color', 'region')
    .style('radius', 4)
    .tooltip({
      title: '区域分布',
      items: [
        {
          name: '工程数量',
          field: 'count',
          valueFormatter: (value: number) => `${value} 个`,
        },
      ],
    })
    .interaction('elementHighlight', true)

  chart.render()
}

/**
 * 更新图表数据
 */
function updateChart() {
  if (!chart) {
    return
  }
  chart.changeData(data.value)
}

/**
 * 更新图表主题
 */
function updateChartTheme() {
  if (!chart) {
    return
  }

  chart.theme({
    type: isDarkTheme.value ? 'dark' : 'light',
  })
  chart.render()
}

/**
 * 销毁图表
 */
function destroyChart() {
  if (chart) {
    chart.destroy()
    chart = null
  }
}

/**
 * 重新渲染图表
 */
function resizeChart() {
  if (chart) {
    chart.forceFit()
  }
}

// ==================== 生命周期 ====================
onMounted(async () => {
  await getUserRegionTree()

  initChart()
})

onUnmounted(() => {
  destroyChart()
})

// ==================== 监听器 ====================

watch(region, (newVal) => {
  const [regionPid, regionId, regionCid] = newVal
  if (regionPid) {
    requestParams.regionPid = regionPid
  }
  if (regionId) {
    requestParams.regionId = regionId
  }
  if (regionCid) {
    requestParams.regionCid = regionCid
  }
  fetchProjectRegionData()
})

// 监听主题变化
watch(
  [isDarkTheme, currentThemeName],
  () => {
    updateChartTheme()
  },
  { deep: true },
)

// 监听窗口大小变化
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  if (chartContainer.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      resizeChart()
    })
    resizeObserver.observe(chartContainer.value)
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})
</script>

<template>
  <Card title="工程区域分布">
    <template #header>
      <div>
        <RegionCascader v-model="region" placeholder="请选择区域" :props="{ checkStrictly: true }" />
      </div>
      <!-- <el-cascader :options="regionOptions" clearable /> -->
      <!-- <el-c
        v-model="selectedRegion" placeholder="选择区域" size="small" style="width: 120px;" :disabled="loading"
        @change="handleRegionChange"
      >
        <el-option v-for="region in regionOptions" :key="region.value" :label="region.label" :value="region.value" />
      </el-select> -->
    </template>
    <div v-loading="loading" class="">
      <div ref="chartContainer" class="min-h-80 w-full" />
    </div>
  </Card>
</template>
