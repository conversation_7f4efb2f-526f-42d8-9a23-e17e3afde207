<!--
  待办事项组件
  显示用户的待办任务列表，支持标签页切换
-->
<script setup lang="ts">
import type { TagProps } from 'element-plus'
import Card from './Card.vue'

/**
 * 待办事项接口
 */
interface TodoItem {
  /** 唯一标识 */
  id?: number
  /** 日期 */
  date: string
  /** 标题 */
  title: string
  /** 优先级 */
  priority: string
}

const data = ref([...Array.from({ length: 10 }).map((_, index) => ({
  id: index + 1,
  date: `05-0${index + 1}`,
  title: `工程【小散工程名称${index + 1}】`,
  priority: index % 3 === 0 ? '高' : index % 3 === 1 ? '中' : '低',
}))])

const radioOptions = ref([
  { label: '我的待办', value: 1 },
  { label: '我的已办', value: 2 },
])

const filterOptions = ref(['今日', '昨日', '全部'])

// ==================== 响应式数据 ====================
const activeTab = ref<number>(1)
const selectedFilters = ref<string>('今日')
const todoList = ref<TodoItem[]>(data.value)

// ==================== 计算属性 ====================
const filteredTodoList = computed(() => {
  // 这里可以根据 activeTab 和 selectedFilters 进行过滤 限制5条
  return todoList.value.slice(0, 5)
})

// ==================== 方法定义 ====================
/**
 * 处理待办项点击
 * @param item - 待办项
 */
function handleTodoClick(item: TodoItem): void {
  // 处理待办项点击
}

/**
 * 处理更多点击
 */
function handleMoreClick(): void {
  // 处理更多点击
}

/**
 * 处理标签页变化
 * @param tab - 标签页
 */
function handleTabChange(tab: string): void {
  // 处理标签页变化
}

/**
 * 获取优先级类型
 * @param priority - 优先级
 * @returns 类型
 */
function getPriorityType(priority: string): TagProps['type'] {
  switch (priority) {
    case '高':
      return 'danger'
    case '中':
      return 'warning'
    case '低':
      return 'info'
    default:
      return 'primary'
  }
}

// ==================== 监听器 ====================
watch(activeTab, (newValue) => {
  handleTabChange(String(newValue))
})
</script>

<template>
  <Card title="待办事项">
    <template #header>
      <el-radio-group v-model="selectedFilters" size="small">
        <el-radio-button v-for="filter in filterOptions" :key="filter" :label="filter">
          {{ filter }}
        </el-radio-button>
      </el-radio-group>
    </template>
    <div class="py-2">
      <el-radio-group v-model="activeTab">
        <el-radio-button
          v-for="item in radioOptions" :key="item.value" v-model="activeTab" :label="item.value"
          size="small"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <div class="flex-1">
      <div class="h-full flex flex-col">
        <div class="flex-1">
          <div
            v-for="item in filteredTodoList" :key="item.id"
            class="flex cursor-pointer items-center py-3 hover:text-primary" @click="handleTodoClick(item)"
          >
            <span class="mr-2 text-sm text-foreground/60">{{ item.date }}</span>
            <span class="flex-1 text-sm leading-relaxed">{{ item.title }}</span>
            <ElTag v-if="item.priority" :type="getPriorityType(item.priority)" size="small" class="ml-2">
              {{ item.priority }}
            </ElTag>
          </div>
        </div>

        <div
          class="flex cursor-pointer items-center justify-center rounded p-2 text-sm text-blue-500 transition-colors duration-300 hover:bg-blue-50"
          @click="handleMoreClick"
        >
          <span class="mr-1">更多</span>
          <FaIcon name="i-ep:arrow-right" />
        </div>
      </div>
    </div>
  </Card>
</template>
