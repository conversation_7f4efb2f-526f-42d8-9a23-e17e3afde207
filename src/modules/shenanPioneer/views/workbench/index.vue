<script setup lang="ts">
import AlarmTrendChart from './components/AlarmTrendChart.vue'
import DataOverview from './components/DataOverview.vue'
import ProjectDistributionChart from './components/ProjectDistributionChart.vue'
import QuickActions from './components/QuickActions.vue'
import ReadingTasks from './components/ReadingTasks.vue'
import TodoTasks from './components/TodoTasks.vue'
</script>

<!--
  工作台首页
  集成数据概览、快捷功能、待办事项、待阅事项、图表等组件
-->
<template>
  <div class="grid grid-cols-2 gap-4 p-4">
    <!-- 数据概览区域 -->
    <section class="grid gap-4">
      <DataOverview />
      <QuickActions />
    </section>

    <!-- 任务管理区域 -->
    <section class="grid grid-cols-2 gap-4">
      <TodoTasks />
      <ReadingTasks />
    </section>

    <!-- 数据分析区域 -->
    <section>
      <AlarmTrendChart />
    </section>

    <section>
      <ProjectDistributionChart />
    </section>
  </div>
</template>
