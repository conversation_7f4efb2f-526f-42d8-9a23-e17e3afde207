import type { App } from 'vue'
import { ServiceSite } from '@admin/service'

import { init as initApi } from '@shencom/api'
import { axios } from './api'
import imgLogoDark from '/assets/logo/logo-dark.svg'
import imgLogo from '/assets/logo/logo.svg'

export default function install(app: App) {
  console.log('%c 当前环境：', 'font-size:13px;font-weight:bold;color:#409eff;', import.meta.env)

  initApi(axios, '')
  ServiceSite.initLogo({ logo: imgLogo, darkLogo: imgLogoDark, loginPage: '/login' })
}
