# 待办事项

帮我完善接口内容

组件文件: `src/modules/shenanPioneer/views/workbench/components/TodoTasks.vue`

url: /xsgc/message/remind/index
post 请求

请求参数

我的待办
查询条件
type in 1,2,3,4,5
resolveFlag = 0

我的已办
查询条件
type in 1,2,3,4,5
resolveFlag = 2

日期查询
通过 createdAt 字段
今天: 今天的开始时间,结束时间
昨天: 昨天的开始时间,结束时间
全部: 不需要传日期查询条件

相应数据
{
	"data": {
		"content": [
			{
				"content": "工程【0725-8】已接入监管",
				"createdAt": 1753442532000,
				"id": "1715972924857315329",
				"orderId": "1715972161085800448",
				"projectId": "1715972161035468801",
				"relateId": "1715972923614461953",
				"resolveFlag": 0,
				"status": 0,
				"type": 6,
				"updatedAt": 1753442532000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【0725-5】已接入监管",
				"createdAt": 1753442308000,
				"id": "1715971983993896960",
				"orderId": "1715969745774870528",
				"projectId": "1715969745720344576",
				"relateId": "1715971983306031106",
				"resolveFlag": 0,
				"status": 0,
				"type": 6,
				"updatedAt": 1753442308000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【0725-4】已接入监管",
				"createdAt": 1753442243000,
				"id": "1715971711196364802",
				"orderId": "1715969745422548992",
				"projectId": "1715969745372217344",
				"relateId": "1715971710676271104",
				"resolveFlag": 0,
				"status": 0,
				"type": 6,
				"updatedAt": 1753442243000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【0725-3】已接入监管",
				"createdAt": 1753442074000,
				"id": "1715971003004911616",
				"orderId": "1715969745103781888",
				"projectId": "1715969744189423617",
				"relateId": "1715971002572898304",
				"resolveFlag": 0,
				"status": 0,
				"type": 6,
				"updatedAt": 1753442074000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【0725-1】已结束监管",
				"createdAt": 1753441679000,
				"id": "1715969345689939969",
				"orderId": "1715939691192320000",
				"projectId": "1715939690038886400",
				"relateId": "1715969344698744832",
				"resolveFlag": 0,
				"status": 0,
				"type": 7,
				"updatedAt": 1753441679000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【0725-1】已接入监管",
				"createdAt": 1753441197000,
				"id": "1715967326975619073",
				"orderId": "1715939691192320000",
				"projectId": "1715939690038886400",
				"relateId": "1715967326676484096",
				"resolveFlag": 0,
				"status": 0,
				"type": 6,
				"updatedAt": 1753441197000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【0725-2】已结束监管",
				"createdAt": 1753441031000,
				"id": "1715966629471252480",
				"orderId": "1715963378863341568",
				"projectId": "1715963378787844097",
				"relateId": "1715966628626857984",
				"resolveFlag": 0,
				"status": 0,
				"type": 7,
				"updatedAt": 1753441031000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【0725-2】已接入监管",
				"createdAt": 1753440509000,
				"id": "1715964437866381312",
				"orderId": "1715963378863341568",
				"projectId": "1715963378787844097",
				"relateId": "1715964437509865474",
				"resolveFlag": 0,
				"status": 0,
				"type": 6,
				"updatedAt": 1753440509000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【测状态12】已结束监管",
				"createdAt": 1753436702000,
				"id": "1715948471120494594",
				"orderId": "1714819938529796096",
				"projectId": "1714819938395578368",
				"relateId": "1715948470625566720",
				"resolveFlag": 0,
				"status": 0,
				"type": 7,
				"updatedAt": 1753436702000,
				"userId": "1711958416497926144"
			},
			{
				"content": "工程【0724-2】已接入监管",
				"createdAt": 1753436186000,
				"id": "1715946307717263360",
				"orderId": "1715468793386070016",
				"projectId": "1715468793197326336",
				"relateId": "1715946305945272320",
				"resolveFlag": 0,
				"status": 0,
				"type": 6,
				"updatedAt": 1753436186000,
				"userId": "1711958416497926144"
			}
		],
		"empty": false,
		"first": true,
		"last": false,
		"number": 0,
		"numberOfElements": 10,
		"pageable": {
			"offset": 0,
			"pageNumber": 0,
			"pageSize": 10,
			"paged": true,
			"sort": {
				"empty": true,
				"sorted": false,
				"unsorted": true
			},
			"unpaged": false
		},
		"size": 10,
		"sort": {
			"empty": true,
			"sorted": false,
			"unsorted": true
		},
		"totalElements": 44,
		"totalPages": 5
	},
	"errcode": "0000",
	"errmsg": "成功"
}
